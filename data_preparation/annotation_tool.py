"""
annotation_tool.py

Interactive annotation tool for human demonstration videos
Allows users to add action labels and task descriptions to video clips
"""

import os
import json
import cv2
import numpy as np
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import threading
import time


class VideoAnnotationTool:
    """Interactive tool for annotating human demonstration videos."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Human Demonstration Video Annotation Tool")
        self.root.geometry("1200x800")
        
        # Video data
        self.current_video_path = None
        self.current_frames = []
        self.current_frame_index = 0
        self.video_fps = 30
        
        # Annotation data
        self.annotations = {}
        self.current_annotation = {
            'instruction': '',
            'actions': [],
            'category': '',
            'notes': ''
        }
        
        # UI components
        self.setup_ui()
        
        # Load existing annotations if available
        self.load_annotations()
    
    def setup_ui(self):
        """Setup the user interface."""
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Top frame for controls
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Video selection
        ttk.Button(control_frame, text="Select Video", command=self.select_video).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="Load Annotations", command=self.load_annotations).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="Save Annotations", command=self.save_annotations).pack(side=tk.LEFT, padx=(0, 10))
        
        # Video info label
        self.video_info_label = ttk.Label(control_frame, text="No video selected")
        self.video_info_label.pack(side=tk.LEFT, padx=(20, 0))
        
        # Main content frame
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # Left panel - Video display
        video_frame = ttk.LabelFrame(content_frame, text="Video Player", padding=10)
        video_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # Video canvas
        self.video_canvas = tk.Canvas(video_frame, bg='black', width=400, height=300)
        self.video_canvas.pack(pady=(0, 10))
        
        # Video controls
        video_controls = ttk.Frame(video_frame)
        video_controls.pack(fill=tk.X)
        
        ttk.Button(video_controls, text="◀◀", command=self.prev_frame).pack(side=tk.LEFT)
        ttk.Button(video_controls, text="▶", command=self.play_pause).pack(side=tk.LEFT, padx=(5, 0))
        ttk.Button(video_controls, text="▶▶", command=self.next_frame).pack(side=tk.LEFT, padx=(5, 0))
        
        # Frame slider
        self.frame_var = tk.IntVar()
        self.frame_slider = ttk.Scale(video_controls, from_=0, to=100, orient=tk.HORIZONTAL, 
                                     variable=self.frame_var, command=self.on_frame_change)
        self.frame_slider.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 0))
        
        # Frame info
        self.frame_info_label = ttk.Label(video_controls, text="Frame: 0/0")
        self.frame_info_label.pack(side=tk.RIGHT)
        
        # Right panel - Annotation controls
        annotation_frame = ttk.LabelFrame(content_frame, text="Annotation", padding=10)
        annotation_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        
        # Task instruction
        ttk.Label(annotation_frame, text="Task Instruction:").pack(anchor=tk.W)
        self.instruction_text = tk.Text(annotation_frame, height=3, width=40)
        self.instruction_text.pack(fill=tk.X, pady=(0, 10))
        
        # Category selection
        ttk.Label(annotation_frame, text="Category:").pack(anchor=tk.W)
        self.category_var = tk.StringVar()
        category_combo = ttk.Combobox(annotation_frame, textvariable=self.category_var,
                                     values=['successful_operation', 'failed_appearance', 'failed_electrical'])
        category_combo.pack(fill=tk.X, pady=(0, 10))
        
        # Action annotation
        ttk.Label(annotation_frame, text="Current Frame Action:").pack(anchor=tk.W)
        
        # Action components
        action_frame = ttk.Frame(annotation_frame)
        action_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Position deltas
        ttk.Label(action_frame, text="Position (dx, dy, dz):").pack(anchor=tk.W)
        pos_frame = ttk.Frame(action_frame)
        pos_frame.pack(fill=tk.X)
        
        self.dx_var = tk.DoubleVar()
        self.dy_var = tk.DoubleVar()
        self.dz_var = tk.DoubleVar()
        
        ttk.Entry(pos_frame, textvariable=self.dx_var, width=8).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Entry(pos_frame, textvariable=self.dy_var, width=8).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Entry(pos_frame, textvariable=self.dz_var, width=8).pack(side=tk.LEFT)
        
        # Rotation deltas
        ttk.Label(action_frame, text="Rotation (drx, dry, drz):").pack(anchor=tk.W, pady=(5, 0))
        rot_frame = ttk.Frame(action_frame)
        rot_frame.pack(fill=tk.X)
        
        self.drx_var = tk.DoubleVar()
        self.dry_var = tk.DoubleVar()
        self.drz_var = tk.DoubleVar()
        
        ttk.Entry(rot_frame, textvariable=self.drx_var, width=8).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Entry(rot_frame, textvariable=self.dry_var, width=8).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Entry(rot_frame, textvariable=self.drz_var, width=8).pack(side=tk.LEFT)
        
        # Gripper action
        ttk.Label(action_frame, text="Gripper:").pack(anchor=tk.W, pady=(5, 0))
        self.gripper_var = tk.DoubleVar()
        ttk.Scale(action_frame, from_=-1, to=1, orient=tk.HORIZONTAL, 
                 variable=self.gripper_var).pack(fill=tk.X)
        
        # Action buttons
        action_buttons = ttk.Frame(annotation_frame)
        action_buttons.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(action_buttons, text="Set Action", command=self.set_current_action).pack(side=tk.LEFT)
        ttk.Button(action_buttons, text="Clear Action", command=self.clear_current_action).pack(side=tk.LEFT, padx=(5, 0))
        
        # Preset actions
        ttk.Label(annotation_frame, text="Preset Actions:").pack(anchor=tk.W, pady=(10, 0))
        preset_frame = ttk.Frame(annotation_frame)
        preset_frame.pack(fill=tk.X)
        
        ttk.Button(preset_frame, text="Move Forward", command=lambda: self.apply_preset_action([0.1, 0, 0, 0, 0, 0, 0])).pack(fill=tk.X)
        ttk.Button(preset_frame, text="Move Down", command=lambda: self.apply_preset_action([0, 0, -0.1, 0, 0, 0, 0])).pack(fill=tk.X)
        ttk.Button(preset_frame, text="Grasp", command=lambda: self.apply_preset_action([0, 0, 0, 0, 0, 0, 1])).pack(fill=tk.X)
        ttk.Button(preset_frame, text="Release", command=lambda: self.apply_preset_action([0, 0, 0, 0, 0, 0, -1])).pack(fill=tk.X)
        
        # Notes
        ttk.Label(annotation_frame, text="Notes:").pack(anchor=tk.W, pady=(10, 0))
        self.notes_text = tk.Text(annotation_frame, height=3, width=40)
        self.notes_text.pack(fill=tk.X)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, pady=(10, 0))
        
        # Playing state
        self.is_playing = False
        self.play_thread = None
    
    def select_video(self):
        """Select a video file to annotate."""
        file_path = filedialog.askopenfilename(
            title="Select Video File",
            filetypes=[("MP4 files", "*.mp4"), ("All files", "*.*")]
        )
        
        if file_path:
            self.load_video(file_path)
    
    def load_video(self, video_path: str):
        """Load a video file."""
        try:
            self.current_video_path = video_path
            
            # Extract frames
            cap = cv2.VideoCapture(video_path)
            self.video_fps = cap.get(cv2.CAP_PROP_FPS)
            
            frames = []
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                # Convert BGR to RGB
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                frames.append(frame_rgb)
            
            cap.release()
            
            self.current_frames = frames
            self.current_frame_index = 0
            
            # Update UI
            video_name = Path(video_path).name
            self.video_info_label.config(text=f"Video: {video_name} ({len(frames)} frames)")
            
            # Setup frame slider
            self.frame_slider.config(to=len(frames) - 1)
            self.frame_var.set(0)
            
            # Load existing annotation if available
            self.load_current_annotation()
            
            # Display first frame
            self.display_current_frame()
            
            self.status_var.set(f"Loaded video: {video_name}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load video: {e}")
    
    def display_current_frame(self):
        """Display the current frame in the canvas."""
        if not self.current_frames:
            return
        
        frame = self.current_frames[self.current_frame_index]
        
        # Resize frame to fit canvas
        canvas_width = self.video_canvas.winfo_width()
        canvas_height = self.video_canvas.winfo_height()
        
        if canvas_width > 1 and canvas_height > 1:
            # Calculate aspect ratio
            frame_height, frame_width = frame.shape[:2]
            aspect_ratio = frame_width / frame_height
            
            if canvas_width / canvas_height > aspect_ratio:
                # Canvas is wider, fit to height
                new_height = canvas_height
                new_width = int(new_height * aspect_ratio)
            else:
                # Canvas is taller, fit to width
                new_width = canvas_width
                new_height = int(new_width / aspect_ratio)
            
            # Resize frame
            frame_resized = cv2.resize(frame, (new_width, new_height))
            
            # Convert to PIL Image and then to PhotoImage
            pil_image = Image.fromarray(frame_resized)
            photo = ImageTk.PhotoImage(pil_image)
            
            # Clear canvas and display image
            self.video_canvas.delete("all")
            self.video_canvas.create_image(canvas_width//2, canvas_height//2, image=photo)
            self.video_canvas.image = photo  # Keep a reference
        
        # Update frame info
        self.frame_info_label.config(text=f"Frame: {self.current_frame_index + 1}/{len(self.current_frames)}")
        
        # Update action fields with current frame's action
        self.load_current_frame_action()
    
    def prev_frame(self):
        """Go to previous frame."""
        if self.current_frames and self.current_frame_index > 0:
            self.current_frame_index -= 1
            self.frame_var.set(self.current_frame_index)
            self.display_current_frame()
    
    def next_frame(self):
        """Go to next frame."""
        if self.current_frames and self.current_frame_index < len(self.current_frames) - 1:
            self.current_frame_index += 1
            self.frame_var.set(self.current_frame_index)
            self.display_current_frame()
    
    def on_frame_change(self, value):
        """Handle frame slider change."""
        if self.current_frames:
            self.current_frame_index = int(float(value))
            self.display_current_frame()
    
    def play_pause(self):
        """Toggle play/pause."""
        if not self.current_frames:
            return
        
        if self.is_playing:
            self.is_playing = False
        else:
            self.is_playing = True
            self.play_thread = threading.Thread(target=self.play_video)
            self.play_thread.start()
    
    def play_video(self):
        """Play video in a separate thread."""
        frame_delay = 1.0 / self.video_fps if self.video_fps > 0 else 1.0 / 30
        
        while self.is_playing and self.current_frame_index < len(self.current_frames) - 1:
            self.current_frame_index += 1
            self.frame_var.set(self.current_frame_index)
            self.root.after(0, self.display_current_frame)
            time.sleep(frame_delay)
        
        self.is_playing = False
    
    def set_current_action(self):
        """Set action for current frame."""
        if not self.current_frames:
            return
        
        action = [
            self.dx_var.get(),
            self.dy_var.get(),
            self.dz_var.get(),
            self.drx_var.get(),
            self.dry_var.get(),
            self.drz_var.get(),
            self.gripper_var.get()
        ]
        
        # Ensure actions list is long enough
        while len(self.current_annotation['actions']) <= self.current_frame_index:
            self.current_annotation['actions'].append([0.0] * 7)
        
        self.current_annotation['actions'][self.current_frame_index] = action
        self.status_var.set(f"Set action for frame {self.current_frame_index + 1}")
    
    def clear_current_action(self):
        """Clear action for current frame."""
        if not self.current_frames:
            return
        
        if self.current_frame_index < len(self.current_annotation['actions']):
            self.current_annotation['actions'][self.current_frame_index] = [0.0] * 7
            self.load_current_frame_action()
            self.status_var.set(f"Cleared action for frame {self.current_frame_index + 1}")
    
    def apply_preset_action(self, action: List[float]):
        """Apply a preset action."""
        self.dx_var.set(action[0])
        self.dy_var.set(action[1])
        self.dz_var.set(action[2])
        self.drx_var.set(action[3])
        self.dry_var.set(action[4])
        self.drz_var.set(action[5])
        self.gripper_var.set(action[6])
    
    def load_current_frame_action(self):
        """Load action for current frame into UI."""
        if (self.current_frame_index < len(self.current_annotation['actions']) and 
            len(self.current_annotation['actions'][self.current_frame_index]) == 7):
            
            action = self.current_annotation['actions'][self.current_frame_index]
            self.dx_var.set(action[0])
            self.dy_var.set(action[1])
            self.dz_var.set(action[2])
            self.drx_var.set(action[3])
            self.dry_var.set(action[4])
            self.drz_var.set(action[5])
            self.gripper_var.set(action[6])
        else:
            # Clear action fields
            self.dx_var.set(0.0)
            self.dy_var.set(0.0)
            self.dz_var.set(0.0)
            self.drx_var.set(0.0)
            self.dry_var.set(0.0)
            self.drz_var.set(0.0)
            self.gripper_var.set(0.0)
    
    def load_current_annotation(self):
        """Load annotation for current video."""
        if not self.current_video_path:
            return
        
        video_name = Path(self.current_video_path).stem
        
        if video_name in self.annotations:
            self.current_annotation = self.annotations[video_name].copy()
        else:
            # Create new annotation
            self.current_annotation = {
                'instruction': self.generate_default_instruction(),
                'actions': [],
                'category': self.get_video_category(),
                'notes': ''
            }
        
        # Update UI
        self.instruction_text.delete(1.0, tk.END)
        self.instruction_text.insert(1.0, self.current_annotation['instruction'])
        
        self.category_var.set(self.current_annotation['category'])
        
        self.notes_text.delete(1.0, tk.END)
        self.notes_text.insert(1.0, self.current_annotation['notes'])
    
    def save_current_annotation(self):
        """Save current annotation."""
        if not self.current_video_path:
            return
        
        # Update annotation from UI
        self.current_annotation['instruction'] = self.instruction_text.get(1.0, tk.END).strip()
        self.current_annotation['category'] = self.category_var.get()
        self.current_annotation['notes'] = self.notes_text.get(1.0, tk.END).strip()
        
        # Save to annotations dict
        video_name = Path(self.current_video_path).stem
        self.annotations[video_name] = self.current_annotation.copy()
    
    def generate_default_instruction(self) -> str:
        """Generate default instruction based on video category."""
        category = self.get_video_category()
        instructions = {
            'successful_operation': 'perform the assembly operation correctly',
            'failed_appearance': 'attempt assembly operation with appearance issues',
            'failed_electrical': 'attempt assembly operation with electrical issues',
            'unknown': 'perform the demonstrated operation'
        }
        return instructions.get(category, 'perform the demonstrated operation')
    
    def get_video_category(self) -> str:
        """Get video category from path."""
        if not self.current_video_path:
            return 'unknown'
        
        if 'segmented_videos_ok' in self.current_video_path:
            return 'successful_operation'
        elif 'segmented_videos_nok_apperence' in self.current_video_path:
            return 'failed_appearance'
        elif 'segmented_videos_nok_ele' in self.current_video_path:
            return 'failed_electrical'
        else:
            return 'unknown'
    
    def load_annotations(self):
        """Load annotations from file."""
        file_path = filedialog.askopenfilename(
            title="Load Annotations",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                with open(file_path, 'r') as f:
                    self.annotations = json.load(f)
                self.status_var.set(f"Loaded annotations from {Path(file_path).name}")
                
                # Reload current annotation if video is loaded
                if self.current_video_path:
                    self.load_current_annotation()
                    
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load annotations: {e}")
    
    def save_annotations(self):
        """Save annotations to file."""
        # Save current annotation first
        self.save_current_annotation()
        
        file_path = filedialog.asksaveasfilename(
            title="Save Annotations",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                with open(file_path, 'w') as f:
                    json.dump(self.annotations, f, indent=2)
                self.status_var.set(f"Saved annotations to {Path(file_path).name}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save annotations: {e}")
    
    def run(self):
        """Run the annotation tool."""
        self.root.mainloop()


def main():
    """Main function."""
    tool = VideoAnnotationTool()
    tool.run()


if __name__ == '__main__':
    main()
