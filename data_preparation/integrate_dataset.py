"""
integrate_dataset.py

Integration script to add custom human demonstration dataset to UniVLA
Modifies the necessary configuration files and creates dataset mixtures
"""

import os
import json
import shutil
from pathlib import Path
from typing import Dict, List, Tuple
import argparse


class DatasetIntegrator:
    """Integrates custom human demonstration dataset into UniVLA."""
    
    def __init__(self, univla_root: str, dataset_name: str = "human_demo"):
        """
        Initialize dataset integrator.
        
        Args:
            univla_root: Path to UniVLA root directory
            dataset_name: Name for the custom dataset
        """
        self.univla_root = Path(univla_root)
        self.dataset_name = dataset_name
        
        # Verify UniVLA directory structure
        self.verify_univla_structure()
    
    def verify_univla_structure(self):
        """Verify that UniVLA directory structure is correct."""
        required_paths = [
            self.univla_root / "prismatic" / "vla" / "datasets" / "rlds" / "oxe",
            self.univla_root / "prismatic" / "vla" / "datasets" / "rlds" / "oxe" / "configs.py",
            self.univla_root / "prismatic" / "vla" / "datasets" / "rlds" / "oxe" / "mixtures.py"
        ]
        
        for path in required_paths:
            if not path.exists():
                raise FileNotFoundError(f"Required UniVLA path not found: {path}")
        
        print("✓ UniVLA directory structure verified")
    
    def copy_dataset_builder(self, source_dir: str):
        """
        Copy dataset builder to UniVLA external datasets directory.
        
        Args:
            source_dir: Path to the dataset builder source directory
        """
        source_path = Path(source_dir)
        target_path = self.univla_root / "vla-scripts" / "extern" / f"{self.dataset_name}_rlds_dataset_builder"
        
        if target_path.exists():
            print(f"Removing existing dataset builder at {target_path}")
            shutil.rmtree(target_path)
        
        print(f"Copying dataset builder from {source_path} to {target_path}")
        shutil.copytree(source_path, target_path)
        
        # Create setup.py for the dataset builder
        setup_content = f'''"""Setup script for {self.dataset_name} dataset builder."""

from setuptools import setup, find_packages

setup(
    name="{self.dataset_name}_dataset_builder",
    version="1.0.0",
    packages=find_packages(),
    install_requires=[
        "tensorflow",
        "tensorflow-datasets",
        "tensorflow-hub",
        "opencv-python",
        "numpy",
        "tqdm",
    ],
    description="RLDS dataset builder for human demonstration videos",
)
'''
        
        with open(target_path / "setup.py", "w") as f:
            f.write(setup_content)
        
        print("✓ Dataset builder copied successfully")
    
    def add_dataset_config(self):
        """Add dataset configuration to OXE configs."""
        configs_path = self.univla_root / "prismatic" / "vla" / "datasets" / "rlds" / "oxe" / "configs.py"
        
        # Read existing configs
        with open(configs_path, 'r') as f:
            content = f.read()
        
        # Check if our dataset is already configured
        if f'"{self.dataset_name}"' in content:
            print(f"Dataset {self.dataset_name} already configured in configs.py")
            return
        
        # Add our dataset configuration
        new_config = f'''    "{self.dataset_name}": {{
        "image_obs_keys": {{"primary": "image", "secondary": None, "wrist": None}},
        "depth_obs_keys": {{"primary": None, "secondary": None, "wrist": None}},
        "state_obs_keys": ["state"],
        "state_encoding": StateEncoding.POS_EULER,
        "action_encoding": ActionEncoding.EEF_POS,
    }},'''
        
        # Find the end of OXE_DATASET_CONFIGS and insert our config
        if "OXE_DATASET_CONFIGS = {" in content:
            # Find the last entry before the closing brace
            lines = content.split('\n')
            insert_index = -1
            
            for i, line in enumerate(lines):
                if line.strip() == '}' and 'OXE_DATASET_CONFIGS' in ''.join(lines[max(0, i-50):i]):
                    insert_index = i
                    break
            
            if insert_index > 0:
                lines.insert(insert_index, new_config)
                content = '\n'.join(lines)
                
                # Write back to file
                with open(configs_path, 'w') as f:
                    f.write(content)
                
                print("✓ Dataset configuration added to configs.py")
            else:
                print("⚠ Could not find insertion point in configs.py")
        else:
            print("⚠ Could not find OXE_DATASET_CONFIGS in configs.py")
    
    def add_dataset_mixture(self, weight: float = 1.0):
        """
        Add dataset to mixtures configuration.
        
        Args:
            weight: Sampling weight for the dataset in mixtures
        """
        mixtures_path = self.univla_root / "prismatic" / "vla" / "datasets" / "rlds" / "oxe" / "mixtures.py"
        
        # Read existing mixtures
        with open(mixtures_path, 'r') as f:
            content = f.read()
        
        # Check if our dataset is already in mixtures
        if f'"{self.dataset_name}"' in content:
            print(f"Dataset {self.dataset_name} already in mixtures.py")
            return
        
        # Add standalone mixture for our dataset
        new_mixture = f'''
    # === Human Demonstration Dataset ===
    "{self.dataset_name}": [
        ("{self.dataset_name}", 1.0),
    ],'''
        
        # Add to existing mixtures
        human_demo_entry = f'        ("{self.dataset_name}", {weight}),'
        
        # Find insertion points
        lines = content.split('\n')
        
        # Add standalone mixture
        for i, line in enumerate(lines):
            if 'OXE_NAMED_MIXTURES: Dict[str, List[Tuple[str, float]]] = {' in line:
                lines.insert(i + 1, new_mixture)
                break
        
        # Add to omni_magic_soup_plus_plus mixture
        in_omni_mixture = False
        for i, line in enumerate(lines):
            if '"omni_magic_soup_plus_plus": [' in line:
                in_omni_mixture = True
            elif in_omni_mixture and line.strip() == '],':
                lines.insert(i, human_demo_entry)
                break
        
        content = '\n'.join(lines)
        
        # Write back to file
        with open(mixtures_path, 'w') as f:
            f.write(content)
        
        print("✓ Dataset mixture added to mixtures.py")
    
    def create_data_preparation_script(self, output_path: str):
        """Create a complete data preparation script."""
        script_content = f'''#!/usr/bin/env python3
"""
Complete data preparation script for {self.dataset_name} dataset.
This script processes raw videos and creates RLDS-formatted dataset.
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """Main data preparation pipeline."""
    print("=== {self.dataset_name.upper()} Data Preparation Pipeline ===")
    
    # Configuration
    data_root = "data"
    processed_dir = "data/processed"
    annotations_file = "data/annotations.json"
    rlds_output_dir = "data/rlds_dataset"
    
    # Step 1: Preprocess videos
    print("\\n1. Preprocessing videos...")
    cmd = [
        "python", "data_preparation/preprocess_videos.py",
        "--data_root", data_root,
        "--output_dir", processed_dir,
        "--target_fps", "10",
        "--max_frames", "32",
        "--num_workers", "4"
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"Error in video preprocessing: {{result.stderr}}")
        return False
    
    print("✓ Video preprocessing completed")
    
    # Step 2: Annotation (manual step)
    print("\\n2. Action annotation...")
    if not Path(annotations_file).exists():
        print("⚠ Annotations file not found. Please run the annotation tool:")
        print("  python data_preparation/annotation_tool.py")
        print("  Save annotations to:", annotations_file)
        return False
    
    print("✓ Annotations found")
    
    # Step 3: Build RLDS dataset
    print("\\n3. Building RLDS dataset...")
    
    # Set up TFDS data directory
    os.environ["TFDS_DATA_DIR"] = rlds_output_dir
    
    # Build dataset
    cmd = [
        "python", "-m", "tensorflow_datasets.scripts.cli.main",
        "build", f"{self.dataset_name}_dataset_builder/{self.dataset_name}",
        "--data_dir", rlds_output_dir
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"Error building RLDS dataset: {{result.stderr}}")
        return False
    
    print("✓ RLDS dataset built successfully")
    
    # Step 4: Validation
    print("\\n4. Validating dataset...")
    cmd = [
        "python", "data_preparation/validate_dataset.py",
        "--dataset_path", rlds_output_dir,
        "--dataset_name", "{self.dataset_name}"
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"Warning: Dataset validation failed: {{result.stderr}}")
    else:
        print("✓ Dataset validation passed")
    
    print("\\n=== Data Preparation Complete ===")
    print(f"RLDS dataset available at: {{rlds_output_dir}}")
    print("\\nNext steps:")
    print("1. Copy the dataset to your UniVLA data directory")
    print("2. Update your training configuration to use the new dataset")
    print("3. Start training with the custom dataset")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
'''
        
        with open(output_path, 'w') as f:
            f.write(script_content)
        
        # Make script executable
        os.chmod(output_path, 0o755)
        
        print(f"✓ Data preparation script created: {output_path}")
    
    def create_training_config(self, output_path: str):
        """Create training configuration for the custom dataset."""
        config_content = f'''"""
Training configuration for {self.dataset_name} dataset.
"""

# Training configuration for human demonstration dataset
HUMAN_DEMO_CONFIG = {{
    # Dataset configuration
    "data_root_dir": "/path/to/your/rlds_data_collection",
    "data_mix": "{self.dataset_name}",  # Use standalone dataset
    # "data_mix": "omni_magic_soup_plus_plus",  # Or use with full mixture
    
    # Model configuration (same as UniVLA defaults)
    "vla_type": "DINOSIGLIP_224PX_MX_BRIDGE",
    "pretrain_vlm": "/path/to/your/prism-dinosiglip-224px_7b",
    "lam_path": "latent_action_model/logs/task_centric_lam_stage2/epoch=0-step=200000.ckpt",
    
    # LAM settings
    "codebook_size": 16,
    "lam_model_dim": 768,
    "lam_latent_dim": 128,
    "lam_patch_size": 14,
    "lam_enc_blocks": 12,
    "lam_dec_blocks": 12,
    "lam_num_heads": 12,
    
    # Training settings
    "batch_size": 8,  # Adjust based on your GPU memory
    "learning_rate": 1e-5,  # Lower LR for fine-tuning
    "max_steps": 10000,
    "save_interval": 1000,
    "eval_interval": 500,
    
    # Data settings specific to short videos
    "shuffle_buffer_size": 1000,  # Smaller buffer for smaller dataset
    "image_aug": True,
    "window_size": 8,  # Suitable for 1-2 second clips
    
    # Logging
    "run_name": "human_demo_finetune",
    "wandb_project": "univla_human_demo",
}}

# Example training command:
# python vla-scripts/train.py \\
#   --data_root_dir /path/to/your/rlds_data_collection \\
#   --data_mix {self.dataset_name} \\
#   --batch_size 8 \\
#   --learning_rate 1e-5 \\
#   --max_steps 10000 \\
#   --run_name human_demo_finetune
'''
        
        with open(output_path, 'w') as f:
            f.write(config_content)
        
        print(f"✓ Training configuration created: {output_path}")
    
    def integrate(self, dataset_builder_dir: str, weight: float = 1.0):
        """
        Complete integration process.
        
        Args:
            dataset_builder_dir: Path to dataset builder source directory
            weight: Sampling weight for dataset in mixtures
        """
        print(f"Integrating {self.dataset_name} dataset into UniVLA...")
        
        try:
            # Copy dataset builder
            self.copy_dataset_builder(dataset_builder_dir)
            
            # Add to configurations
            self.add_dataset_config()
            self.add_dataset_mixture(weight)
            
            # Create helper scripts
            self.create_data_preparation_script("data_preparation/prepare_dataset.py")
            self.create_training_config("data_preparation/training_config.py")
            
            print(f"\\n✅ Integration complete!")
            print(f"\\nNext steps:")
            print(f"1. Run: python data_preparation/prepare_dataset.py")
            print(f"2. Use annotation tool to label your videos")
            print(f"3. Build RLDS dataset")
            print(f"4. Start training with your custom dataset")
            
        except Exception as e:
            print(f"❌ Integration failed: {e}")
            raise


def main():
    """Main function for command line usage."""
    parser = argparse.ArgumentParser(description='Integrate custom dataset into UniVLA')
    parser.add_argument('--univla_root', type=str, required=True,
                       help='Path to UniVLA root directory')
    parser.add_argument('--dataset_builder_dir', type=str, required=True,
                       help='Path to dataset builder source directory')
    parser.add_argument('--dataset_name', type=str, default='human_demo',
                       help='Name for the custom dataset')
    parser.add_argument('--weight', type=float, default=1.0,
                       help='Sampling weight for dataset in mixtures')
    
    args = parser.parse_args()
    
    # Create integrator and run integration
    integrator = DatasetIntegrator(args.univla_root, args.dataset_name)
    integrator.integrate(args.dataset_builder_dir, args.weight)


if __name__ == '__main__':
    main()
