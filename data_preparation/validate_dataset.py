"""
validate_dataset.py

Dataset validation and testing script for human demonstration RLDS dataset
Provides quality checks, format validation, and visualization tools
"""

import os
import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import tensorflow as tf
import tensorflow_datasets as tfds
from typing import Dict, List, Tuple, Optional, Any
import argparse
from tqdm import tqdm
import cv2
from PIL import Image


class DatasetValidator:
    """Validator for human demonstration RLDS dataset."""
    
    def __init__(self, dataset_path: str, dataset_name: str):
        """
        Initialize dataset validator.
        
        Args:
            dataset_path: Path to RLDS dataset directory
            dataset_name: Name of the dataset
        """
        self.dataset_path = Path(dataset_path)
        self.dataset_name = dataset_name
        self.validation_results = {}
    
    def validate_dataset_structure(self) -> bool:
        """Validate basic dataset structure."""
        print("Validating dataset structure...")
        
        try:
            # Check if dataset directory exists
            if not self.dataset_path.exists():
                self.validation_results['structure'] = {
                    'valid': False,
                    'error': f"Dataset path does not exist: {self.dataset_path}"
                }
                return False
            
            # Try to load dataset
            ds = tfds.load(
                self.dataset_name,
                data_dir=str(self.dataset_path),
                split='train',
                as_supervised=False
            )
            
            # Check if we can iterate through dataset
            sample_count = 0
            for sample in ds.take(1):
                sample_count += 1
                
                # Validate sample structure
                required_keys = ['steps', 'episode_metadata']
                for key in required_keys:
                    if key not in sample:
                        self.validation_results['structure'] = {
                            'valid': False,
                            'error': f"Missing required key: {key}"
                        }
                        return False
                
                # Validate steps structure
                steps = sample['steps']
                for step in steps.take(1):
                    step_keys = ['observation', 'action', 'reward', 'is_first', 'is_last', 'language_instruction']
                    for key in step_keys:
                        if key not in step:
                            self.validation_results['structure'] = {
                                'valid': False,
                                'error': f"Missing step key: {key}"
                            }
                            return False
            
            self.validation_results['structure'] = {
                'valid': True,
                'sample_count': sample_count
            }
            
            print("✓ Dataset structure validation passed")
            return True
            
        except Exception as e:
            self.validation_results['structure'] = {
                'valid': False,
                'error': str(e)
            }
            print(f"❌ Dataset structure validation failed: {e}")
            return False
    
    def validate_data_types(self) -> bool:
        """Validate data types and shapes."""
        print("Validating data types and shapes...")
        
        try:
            ds = tfds.load(
                self.dataset_name,
                data_dir=str(self.dataset_path),
                split='train',
                as_supervised=False
            )
            
            type_errors = []
            shape_info = {}
            
            for episode_idx, episode in enumerate(ds.take(5)):  # Check first 5 episodes
                steps = episode['steps']
                
                for step_idx, step in enumerate(steps):
                    # Check observation types and shapes
                    obs = step['observation']
                    
                    # Image should be uint8 with shape (224, 224, 3)
                    if 'image' in obs:
                        image = obs['image']
                        if image.dtype != tf.uint8:
                            type_errors.append(f"Episode {episode_idx}, Step {step_idx}: image dtype is {image.dtype}, expected uint8")
                        
                        expected_shape = (224, 224, 3)
                        if tuple(image.shape) != expected_shape:
                            type_errors.append(f"Episode {episode_idx}, Step {step_idx}: image shape is {image.shape}, expected {expected_shape}")
                        
                        shape_info['image'] = tuple(image.shape)
                    
                    # Action should be float32 with shape (7,)
                    if 'action' in step:
                        action = step['action']
                        if action.dtype != tf.float32:
                            type_errors.append(f"Episode {episode_idx}, Step {step_idx}: action dtype is {action.dtype}, expected float32")
                        
                        expected_shape = (7,)
                        if tuple(action.shape) != expected_shape:
                            type_errors.append(f"Episode {episode_idx}, Step {step_idx}: action shape is {action.shape}, expected {expected_shape}")
                        
                        shape_info['action'] = tuple(action.shape)
                    
                    # State should be float32 with shape (7,)
                    if 'state' in obs:
                        state = obs['state']
                        if state.dtype != tf.float32:
                            type_errors.append(f"Episode {episode_idx}, Step {step_idx}: state dtype is {state.dtype}, expected float32")
                        
                        shape_info['state'] = tuple(state.shape)
            
            self.validation_results['data_types'] = {
                'valid': len(type_errors) == 0,
                'errors': type_errors,
                'shape_info': shape_info
            }
            
            if len(type_errors) == 0:
                print("✓ Data types and shapes validation passed")
                return True
            else:
                print(f"❌ Data types validation failed with {len(type_errors)} errors")
                for error in type_errors[:5]:  # Show first 5 errors
                    print(f"  - {error}")
                return False
                
        except Exception as e:
            self.validation_results['data_types'] = {
                'valid': False,
                'error': str(e)
            }
            print(f"❌ Data types validation failed: {e}")
            return False
    
    def analyze_dataset_statistics(self) -> Dict:
        """Analyze dataset statistics."""
        print("Analyzing dataset statistics...")
        
        try:
            ds = tfds.load(
                self.dataset_name,
                data_dir=str(self.dataset_path),
                split='train',
                as_supervised=False
            )
            
            stats = {
                'total_episodes': 0,
                'total_steps': 0,
                'episode_lengths': [],
                'action_stats': {
                    'mean': np.zeros(7),
                    'std': np.zeros(7),
                    'min': np.full(7, float('inf')),
                    'max': np.full(7, float('-inf'))
                },
                'categories': {},
                'instructions': []
            }
            
            all_actions = []
            
            for episode in tqdm(ds, desc="Analyzing episodes"):
                episode_length = 0
                steps = episode['steps']
                
                # Get episode metadata
                metadata = episode['episode_metadata']
                category = metadata.get('video_category', b'unknown').numpy().decode('utf-8')
                stats['categories'][category] = stats['categories'].get(category, 0) + 1
                
                for step in steps:
                    episode_length += 1
                    stats['total_steps'] += 1
                    
                    # Collect action data
                    action = step['action'].numpy()
                    all_actions.append(action)
                    
                    # Update action statistics
                    stats['action_stats']['min'] = np.minimum(stats['action_stats']['min'], action)
                    stats['action_stats']['max'] = np.maximum(stats['action_stats']['max'], action)
                    
                    # Collect language instructions
                    if 'language_instruction' in step:
                        instruction = step['language_instruction'].numpy().decode('utf-8')
                        if instruction not in stats['instructions']:
                            stats['instructions'].append(instruction)
                
                stats['episode_lengths'].append(episode_length)
                stats['total_episodes'] += 1
            
            # Calculate action statistics
            if all_actions:
                all_actions = np.array(all_actions)
                stats['action_stats']['mean'] = np.mean(all_actions, axis=0)
                stats['action_stats']['std'] = np.std(all_actions, axis=0)
            
            # Calculate derived statistics
            stats['avg_episode_length'] = np.mean(stats['episode_lengths']) if stats['episode_lengths'] else 0
            stats['min_episode_length'] = np.min(stats['episode_lengths']) if stats['episode_lengths'] else 0
            stats['max_episode_length'] = np.max(stats['episode_lengths']) if stats['episode_lengths'] else 0
            
            self.validation_results['statistics'] = stats
            
            print(f"✓ Dataset statistics analysis complete")
            print(f"  - Total episodes: {stats['total_episodes']}")
            print(f"  - Total steps: {stats['total_steps']}")
            print(f"  - Average episode length: {stats['avg_episode_length']:.1f}")
            print(f"  - Categories: {list(stats['categories'].keys())}")
            
            return stats
            
        except Exception as e:
            print(f"❌ Statistics analysis failed: {e}")
            return {}
    
    def visualize_dataset(self, output_dir: str, num_samples: int = 5):
        """Create visualizations of the dataset."""
        print(f"Creating dataset visualizations...")
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        try:
            ds = tfds.load(
                self.dataset_name,
                data_dir=str(self.dataset_path),
                split='train',
                as_supervised=False
            )
            
            # Visualize sample episodes
            fig, axes = plt.subplots(num_samples, 4, figsize=(16, 4 * num_samples))
            if num_samples == 1:
                axes = axes.reshape(1, -1)
            
            episode_count = 0
            for episode in ds.take(num_samples):
                steps = episode['steps']
                metadata = episode['episode_metadata']
                
                category = metadata.get('video_category', b'unknown').numpy().decode('utf-8')
                
                # Get first few frames from episode
                frames = []
                actions = []
                instructions = []
                
                for step_idx, step in enumerate(steps.take(4)):
                    frames.append(step['observation']['image'].numpy())
                    actions.append(step['action'].numpy())
                    instructions.append(step['language_instruction'].numpy().decode('utf-8'))
                
                # Plot frames
                for frame_idx, frame in enumerate(frames):
                    ax = axes[episode_count, frame_idx]
                    ax.imshow(frame)
                    ax.set_title(f"Episode {episode_count + 1}, Frame {frame_idx + 1}\\n{category}")
                    ax.axis('off')
                    
                    # Add action annotation
                    action = actions[frame_idx]
                    action_text = f"Action: [{action[0]:.2f}, {action[1]:.2f}, {action[2]:.2f}, {action[6]:.2f}]"
                    ax.text(0.02, 0.98, action_text, transform=ax.transAxes, 
                           verticalalignment='top', fontsize=8, 
                           bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
                
                episode_count += 1
            
            plt.tight_layout()
            plt.savefig(output_path / 'sample_episodes.png', dpi=150, bbox_inches='tight')
            plt.close()
            
            # Plot action distributions
            if 'statistics' in self.validation_results:
                stats = self.validation_results['statistics']
                
                fig, axes = plt.subplots(2, 4, figsize=(16, 8))
                action_names = ['dx', 'dy', 'dz', 'drx', 'dry', 'drz', 'gripper']
                
                for i, name in enumerate(action_names):
                    row = i // 4
                    col = i % 4
                    
                    # Create histogram of action values
                    # Note: This is simplified - in practice you'd collect all action values
                    mean_val = stats['action_stats']['mean'][i]
                    std_val = stats['action_stats']['std'][i]
                    
                    axes[row, col].bar([name], [mean_val], yerr=[std_val], capsize=5)
                    axes[row, col].set_title(f'{name} (mean ± std)')
                    axes[row, col].set_ylabel('Value')
                
                # Remove empty subplot
                if len(action_names) < 8:
                    fig.delaxes(axes[1, 3])
                
                plt.tight_layout()
                plt.savefig(output_path / 'action_statistics.png', dpi=150, bbox_inches='tight')
                plt.close()
                
                # Plot episode length distribution
                plt.figure(figsize=(10, 6))
                plt.hist(stats['episode_lengths'], bins=20, alpha=0.7, edgecolor='black')
                plt.xlabel('Episode Length (frames)')
                plt.ylabel('Frequency')
                plt.title('Distribution of Episode Lengths')
                plt.grid(True, alpha=0.3)
                plt.savefig(output_path / 'episode_lengths.png', dpi=150, bbox_inches='tight')
                plt.close()
                
                # Plot category distribution
                if stats['categories']:
                    plt.figure(figsize=(10, 6))
                    categories = list(stats['categories'].keys())
                    counts = list(stats['categories'].values())
                    
                    plt.bar(categories, counts)
                    plt.xlabel('Category')
                    plt.ylabel('Number of Episodes')
                    plt.title('Distribution of Video Categories')
                    plt.xticks(rotation=45)
                    plt.tight_layout()
                    plt.savefig(output_path / 'category_distribution.png', dpi=150, bbox_inches='tight')
                    plt.close()
            
            print(f"✓ Visualizations saved to {output_path}")
            
        except Exception as e:
            print(f"❌ Visualization failed: {e}")
    
    def validate_compatibility(self) -> bool:
        """Validate compatibility with UniVLA training pipeline."""
        print("Validating UniVLA compatibility...")
        
        try:
            # Check if dataset can be loaded with UniVLA's expected format
            ds = tfds.load(
                self.dataset_name,
                data_dir=str(self.dataset_path),
                split='train',
                as_supervised=False
            )
            
            compatibility_issues = []
            
            for episode in ds.take(1):
                steps = episode['steps']
                
                for step in steps.take(1):
                    # Check observation format
                    obs = step['observation']
                    
                    # UniVLA expects 'image_primary' key
                    if 'image' in obs and 'image_primary' not in obs:
                        compatibility_issues.append("Dataset uses 'image' key instead of 'image_primary'")
                    
                    # Check action format (should be 7-dimensional)
                    action = step['action']
                    if len(action.shape) != 1 or action.shape[0] != 7:
                        compatibility_issues.append(f"Action shape {action.shape} is not compatible (expected: (7,))")
                    
                    # Check language instruction format
                    if 'language_instruction' not in step:
                        compatibility_issues.append("Missing language_instruction field")
            
            self.validation_results['compatibility'] = {
                'valid': len(compatibility_issues) == 0,
                'issues': compatibility_issues
            }
            
            if len(compatibility_issues) == 0:
                print("✓ UniVLA compatibility validation passed")
                return True
            else:
                print(f"⚠ UniVLA compatibility issues found:")
                for issue in compatibility_issues:
                    print(f"  - {issue}")
                return False
                
        except Exception as e:
            self.validation_results['compatibility'] = {
                'valid': False,
                'error': str(e)
            }
            print(f"❌ Compatibility validation failed: {e}")
            return False
    
    def generate_report(self, output_path: str):
        """Generate comprehensive validation report."""
        report = {
            'dataset_name': self.dataset_name,
            'dataset_path': str(self.dataset_path),
            'validation_results': self.validation_results,
            'summary': {
                'structure_valid': self.validation_results.get('structure', {}).get('valid', False),
                'types_valid': self.validation_results.get('data_types', {}).get('valid', False),
                'compatibility_valid': self.validation_results.get('compatibility', {}).get('valid', False),
                'overall_valid': all([
                    self.validation_results.get('structure', {}).get('valid', False),
                    self.validation_results.get('data_types', {}).get('valid', False),
                    self.validation_results.get('compatibility', {}).get('valid', False)
                ])
            }
        }
        
        with open(output_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"✓ Validation report saved to {output_path}")
        return report
    
    def run_full_validation(self, output_dir: str = "validation_output"):
        """Run complete validation pipeline."""
        print(f"Running full validation for dataset: {self.dataset_name}")
        print("=" * 60)
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Run all validation steps
        structure_valid = self.validate_dataset_structure()
        types_valid = self.validate_data_types()
        self.analyze_dataset_statistics()
        compatibility_valid = self.validate_compatibility()
        
        # Generate visualizations
        self.visualize_dataset(output_dir)
        
        # Generate report
        report = self.generate_report(output_path / 'validation_report.json')
        
        # Print summary
        print("\\n" + "=" * 60)
        print("VALIDATION SUMMARY")
        print("=" * 60)
        print(f"Dataset Structure: {'✓ PASS' if structure_valid else '❌ FAIL'}")
        print(f"Data Types: {'✓ PASS' if types_valid else '❌ FAIL'}")
        print(f"UniVLA Compatibility: {'✓ PASS' if compatibility_valid else '⚠ ISSUES'}")
        
        if 'statistics' in self.validation_results:
            stats = self.validation_results['statistics']
            print(f"\\nDataset Statistics:")
            print(f"  - Episodes: {stats['total_episodes']}")
            print(f"  - Steps: {stats['total_steps']}")
            print(f"  - Avg Episode Length: {stats['avg_episode_length']:.1f}")
        
        overall_status = report['summary']['overall_valid']
        print(f"\\nOverall Status: {'✅ READY FOR TRAINING' if overall_status else '❌ NEEDS FIXES'}")
        
        return report


def main():
    """Main function for command line usage."""
    parser = argparse.ArgumentParser(description='Validate human demonstration RLDS dataset')
    parser.add_argument('--dataset_path', type=str, required=True,
                       help='Path to RLDS dataset directory')
    parser.add_argument('--dataset_name', type=str, required=True,
                       help='Name of the dataset')
    parser.add_argument('--output_dir', type=str, default='validation_output',
                       help='Output directory for validation results')
    
    args = parser.parse_args()
    
    # Create validator and run validation
    validator = DatasetValidator(args.dataset_path, args.dataset_name)
    report = validator.run_full_validation(args.output_dir)
    
    # Exit with appropriate code
    exit_code = 0 if report['summary']['overall_valid'] else 1
    exit(exit_code)


if __name__ == '__main__':
    main()
