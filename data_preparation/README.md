# 人类演示视频数据准备指南

本指南将帮助您将1-2秒的人类演示视频数据转换为UniVLA兼容的RLDS格式，并进行模型微调。

## 目录结构

```
data_preparation/
├── README.md                           # 本指南
├── human_demo_dataset_builder/         # RLDS数据集构建器
│   └── human_demo/
│       ├── __init__.py
│       └── human_demo_dataset_builder.py
├── preprocess_videos.py               # 视频预处理脚本
├── annotation_tool.py                 # 交互式标注工具
├── integrate_dataset.py               # 数据集集成脚本
├── validate_dataset.py                # 数据验证脚本
└── training_config.py                 # 训练配置（集成后生成）
```

## 第一步：环境准备

### 1.1 安装依赖

```bash
# 基础依赖
pip install tensorflow tensorflow-datasets tensorflow-hub
pip install opencv-python pillow numpy matplotlib seaborn
pip install tqdm pathlib

# GUI依赖（用于标注工具）
pip install tkinter  # 通常已预装

# 可选：并行处理
pip install multiprocessing
```

### 1.2 数据目录结构

确保您的视频数据按以下结构组织：

```
data/
├── segmented_videos_ok/                # 成功操作视频
│   ├── video1.mp4
│   ├── video2.mp4
│   └── ...
├── segmented_videos_nok_apperence/     # 外观问题视频
│   ├── video1.mp4
│   └── ...
└── segmented_videos_nok_ele/           # 电气问题视频
    ├── video1.mp4
    └── ...
```

## 第二步：视频预处理

### 2.1 运行预处理脚本

```bash
python data_preparation/preprocess_videos.py \
    --data_root data \
    --output_dir data/processed \
    --target_fps 10 \
    --max_frames 32 \
    --min_frames 5 \
    --num_workers 4 \
    --resolution 224 224
```

**参数说明：**
- `--data_root`: 原始视频数据根目录
- `--output_dir`: 处理后数据输出目录
- `--target_fps`: 目标帧率（推荐10fps用于1-2秒视频）
- `--max_frames`: 每个视频最大帧数
- `--min_frames`: 每个视频最小帧数
- `--num_workers`: 并行处理进程数
- `--resolution`: 目标分辨率（UniVLA要求224x224）

### 2.2 预处理结果

预处理完成后，您将得到：
- `data/processed/`: 包含每个视频的帧文件夹
- `data/processed/processing_summary.json`: 处理摘要

## 第三步：动作标注

### 3.1 启动标注工具

```bash
python data_preparation/annotation_tool.py
```

### 3.2 使用标注工具

1. **选择视频**: 点击"Select Video"选择要标注的视频文件
2. **播放控制**: 使用播放按钮和滑块浏览视频帧
3. **设置指令**: 在"Task Instruction"框中输入任务描述
4. **选择类别**: 从下拉菜单选择视频类别
5. **标注动作**: 
   - 为每一帧设置动作参数（位置、旋转、夹爪）
   - 使用预设动作快速标注常见操作
   - 点击"Set Action"保存当前帧的动作
6. **保存标注**: 点击"Save Annotations"保存到JSON文件

### 3.3 动作标注指南

**动作向量格式**: `[dx, dy, dz, drx, dry, drz, gripper]`

- `dx, dy, dz`: 位置变化（米）
- `drx, dry, drz`: 旋转变化（弧度）
- `gripper`: 夹爪动作（-1=张开，1=闭合，0=保持）

**标注建议**：
- 对于1-2秒短视频，重点标注关键动作
- 使用相对较小的动作值（0.01-0.1米的位置变化）
- 确保动作序列的连续性和合理性

## 第四步：数据集集成

### 4.1 集成到UniVLA

```bash
python data_preparation/integrate_dataset.py \
    --univla_root /path/to/UniVLA \
    --dataset_builder_dir data_preparation/human_demo_dataset_builder \
    --dataset_name human_demo \
    --weight 1.0
```

**参数说明：**
- `--univla_root`: UniVLA项目根目录路径
- `--dataset_builder_dir`: 数据集构建器源目录
- `--dataset_name`: 自定义数据集名称
- `--weight`: 在数据混合中的采样权重

### 4.2 集成结果

集成完成后，将自动：
- 复制数据集构建器到UniVLA项目
- 更新配置文件（configs.py, mixtures.py）
- 生成数据准备脚本和训练配置

## 第五步：构建RLDS数据集

### 5.1 运行数据准备脚本

```bash
python data_preparation/prepare_dataset.py
```

这个脚本将自动执行：
1. 视频预处理
2. 检查标注文件
3. 构建RLDS数据集
4. 数据验证

### 5.2 手动构建（可选）

如果自动脚本失败，可以手动构建：

```bash
# 设置TFDS数据目录
export TFDS_DATA_DIR=data/rlds_dataset

# 构建数据集
python -m tensorflow_datasets.scripts.cli.main build \
    human_demo_dataset_builder/human_demo \
    --data_dir data/rlds_dataset
```

## 第六步：数据验证

### 6.1 运行验证脚本

```bash
python data_preparation/validate_dataset.py \
    --dataset_path data/rlds_dataset \
    --dataset_name human_demo \
    --output_dir validation_output
```

### 6.2 验证内容

验证脚本将检查：
- **数据结构**: RLDS格式正确性
- **数据类型**: 图像、动作、状态数据类型
- **兼容性**: 与UniVLA训练pipeline的兼容性
- **统计分析**: 数据集统计信息
- **可视化**: 生成样本图像和统计图表

### 6.3 验证结果

验证完成后，查看：
- `validation_output/validation_report.json`: 详细验证报告
- `validation_output/sample_episodes.png`: 样本视频帧
- `validation_output/action_statistics.png`: 动作统计
- `validation_output/episode_lengths.png`: 视频长度分布

## 第七步：模型训练

### 7.1 训练配置

使用生成的训练配置：

```python
# 查看 data_preparation/training_config.py
HUMAN_DEMO_CONFIG = {
    "data_root_dir": "/path/to/your/rlds_data_collection",
    "data_mix": "human_demo",
    "batch_size": 8,
    "learning_rate": 1e-5,
    "max_steps": 10000,
    # ... 其他配置
}
```

### 7.2 启动训练

```bash
cd UniVLA

python vla-scripts/train.py \
    --data_root_dir /path/to/your/rlds_data_collection \
    --data_mix human_demo \
    --batch_size 8 \
    --learning_rate 1e-5 \
    --max_steps 10000 \
    --run_name human_demo_finetune
```

## 针对短视频的特殊处理

### 短视频优化策略

1. **帧采样**: 使用较低的目标帧率（10fps）以保留关键信息
2. **窗口大小**: 设置较小的window_size（8-16帧）
3. **数据增强**: 启用图像增强以增加数据多样性
4. **学习率**: 使用较低的学习率进行微调

### 数据质量建议

1. **动作连续性**: 确保相邻帧之间的动作变化合理
2. **标注一致性**: 相似操作使用一致的动作标注
3. **类别平衡**: 尽量平衡不同类别的样本数量
4. **质量检查**: 使用验证工具检查数据质量

## 故障排除

### 常见问题

1. **视频加载失败**
   - 检查视频格式（推荐MP4）
   - 确认OpenCV安装正确

2. **内存不足**
   - 减少并行进程数
   - 降低最大帧数限制

3. **RLDS构建失败**
   - 检查TensorFlow和TFDS版本
   - 确认数据路径正确

4. **训练兼容性问题**
   - 运行数据验证脚本
   - 检查动作维度和数据类型

### 调试技巧

1. **启用详细日志**:
   ```bash
   export TF_CPP_MIN_LOG_LEVEL=0
   ```

2. **检查数据样本**:
   ```python
   import tensorflow_datasets as tfds
   ds = tfds.load('human_demo', split='train')
   for sample in ds.take(1):
       print(sample)
   ```

3. **可视化检查**:
   使用验证脚本生成的可视化结果检查数据质量

## 总结

通过以上步骤，您可以成功将1-2秒的人类演示视频转换为UniVLA兼容的RLDS格式，并进行模型微调。关键要点：

1. **数据预处理**: 正确的帧采样和尺寸调整
2. **动作标注**: 准确且一致的动作标注
3. **格式转换**: 符合RLDS规范的数据结构
4. **质量验证**: 全面的数据验证和可视化检查
5. **训练优化**: 针对短视频的训练参数调整

如有问题，请参考故障排除部分或检查生成的验证报告。
