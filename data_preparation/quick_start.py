#!/usr/bin/env python3
"""
quick_start.py

快速开始脚本 - 一键完成人类演示视频数据准备
Quick start script for human demonstration video data preparation
"""

import os
import sys
import subprocess
import json
from pathlib import Path
import argparse
from typing import Dict, List


def check_dependencies():
    """检查必要的依赖包"""
    print("检查依赖包...")
    
    required_packages = [
        'tensorflow',
        'tensorflow_datasets', 
        'tensorflow_hub',
        'opencv-python',
        'numpy',
        'matplotlib',
        'pillow',
        'tqdm'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✓ 所有依赖包已安装")
    return True


def check_data_structure(data_root: str) -> Dict:
    """检查数据目录结构"""
    print("检查数据目录结构...")
    
    data_path = Path(data_root)
    if not data_path.exists():
        print(f"❌ 数据根目录不存在: {data_root}")
        return {'valid': False, 'error': f'数据根目录不存在: {data_root}'}
    
    expected_dirs = [
        'segmented_videos_ok',
        'segmented_videos_nok_apperence', 
        'segmented_videos_nok_ele'
    ]
    
    found_dirs = []
    video_counts = {}
    
    for dir_name in expected_dirs:
        dir_path = data_path / dir_name
        if dir_path.exists():
            found_dirs.append(dir_name)
            video_files = list(dir_path.glob('*.mp4'))
            video_counts[dir_name] = len(video_files)
            print(f"  ✓ {dir_name}: {len(video_files)} 个视频文件")
        else:
            print(f"  ⚠ {dir_name}: 目录不存在")
    
    if not found_dirs:
        return {'valid': False, 'error': '未找到任何视频目录'}
    
    total_videos = sum(video_counts.values())
    print(f"✓ 总共找到 {total_videos} 个视频文件")
    
    return {
        'valid': True,
        'found_dirs': found_dirs,
        'video_counts': video_counts,
        'total_videos': total_videos
    }


def run_preprocessing(data_root: str, output_dir: str, config: Dict) -> bool:
    """运行视频预处理"""
    print("\n=== 步骤1: 视频预处理 ===")
    
    cmd = [
        sys.executable, "data_preparation/preprocess_videos.py",
        "--data_root", data_root,
        "--output_dir", output_dir,
        "--target_fps", str(config.get('target_fps', 10)),
        "--max_frames", str(config.get('max_frames', 32)),
        "--min_frames", str(config.get('min_frames', 5)),
        "--num_workers", str(config.get('num_workers', 4)),
        "--resolution", str(config.get('resolution', [224, 224])[0]), str(config.get('resolution', [224, 224])[1])
    ]
    
    print(f"运行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print("✓ 视频预处理完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 视频预处理失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False


def launch_annotation_tool():
    """启动标注工具"""
    print("\n=== 步骤2: 动作标注 ===")
    print("启动交互式标注工具...")
    print("请在标注工具中:")
    print("1. 选择视频文件进行标注")
    print("2. 为每个视频添加任务指令和动作标签")
    print("3. 保存标注到 data/annotations.json")
    print("4. 完成所有视频标注后关闭工具")
    
    input("按回车键启动标注工具...")
    
    try:
        cmd = [sys.executable, "data_preparation/annotation_tool.py"]
        subprocess.run(cmd, check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 标注工具启动失败: {e}")
        return False
    except KeyboardInterrupt:
        print("标注工具已关闭")
        return True


def check_annotations(annotations_file: str) -> bool:
    """检查标注文件"""
    print("检查标注文件...")
    
    if not Path(annotations_file).exists():
        print(f"❌ 标注文件不存在: {annotations_file}")
        print("请使用标注工具创建标注文件")
        return False
    
    try:
        with open(annotations_file, 'r') as f:
            annotations = json.load(f)
        
        if not annotations:
            print("⚠ 标注文件为空")
            return False
        
        print(f"✓ 找到 {len(annotations)} 个视频的标注")
        
        # 检查标注质量
        incomplete_annotations = []
        for video_name, annotation in annotations.items():
            if not annotation.get('instruction'):
                incomplete_annotations.append(f"{video_name}: 缺少任务指令")
            if not annotation.get('actions'):
                incomplete_annotations.append(f"{video_name}: 缺少动作标注")
        
        if incomplete_annotations:
            print("⚠ 发现不完整的标注:")
            for issue in incomplete_annotations[:5]:  # 只显示前5个
                print(f"  - {issue}")
            if len(incomplete_annotations) > 5:
                print(f"  ... 还有 {len(incomplete_annotations) - 5} 个问题")
        
        return True
        
    except Exception as e:
        print(f"❌ 标注文件格式错误: {e}")
        return False


def build_rlds_dataset(dataset_name: str, output_dir: str) -> bool:
    """构建RLDS数据集"""
    print("\n=== 步骤3: 构建RLDS数据集 ===")
    
    # 设置环境变量
    os.environ["TFDS_DATA_DIR"] = output_dir
    
    cmd = [
        sys.executable, "-m", "tensorflow_datasets.scripts.cli.main",
        "build", f"data_preparation/human_demo_dataset_builder/human_demo",
        "--data_dir", output_dir
    ]
    
    print(f"运行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print("✓ RLDS数据集构建完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ RLDS数据集构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False


def validate_dataset(dataset_path: str, dataset_name: str, output_dir: str) -> bool:
    """验证数据集"""
    print("\n=== 步骤4: 数据集验证 ===")
    
    cmd = [
        sys.executable, "data_preparation/validate_dataset.py",
        "--dataset_path", dataset_path,
        "--dataset_name", dataset_name,
        "--output_dir", output_dir
    ]
    
    print(f"运行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print("✓ 数据集验证通过")
        return True
    except subprocess.CalledProcessError as e:
        print(f"⚠ 数据集验证发现问题: {e}")
        print(f"详细信息: {e.stderr}")
        print("请查看验证报告了解详情")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='人类演示视频数据准备快速开始脚本')
    parser.add_argument('--data_root', type=str, default='data',
                       help='视频数据根目录 (默认: data)')
    parser.add_argument('--dataset_name', type=str, default='human_demo',
                       help='数据集名称 (默认: human_demo)')
    parser.add_argument('--skip_preprocessing', action='store_true',
                       help='跳过视频预处理步骤')
    parser.add_argument('--skip_annotation', action='store_true',
                       help='跳过动作标注步骤')
    parser.add_argument('--config', type=str,
                       help='配置文件路径 (JSON格式)')
    
    args = parser.parse_args()
    
    print("🚀 人类演示视频数据准备 - 快速开始")
    print("=" * 50)
    
    # 加载配置
    config = {
        'target_fps': 10,
        'max_frames': 32,
        'min_frames': 5,
        'num_workers': 4,
        'resolution': [224, 224]
    }
    
    if args.config and Path(args.config).exists():
        with open(args.config, 'r') as f:
            user_config = json.load(f)
            config.update(user_config)
        print(f"✓ 加载配置文件: {args.config}")
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    # 检查数据结构
    data_info = check_data_structure(args.data_root)
    if not data_info['valid']:
        print(f"❌ {data_info['error']}")
        return False
    
    # 设置输出路径
    processed_dir = "data/processed"
    rlds_output_dir = "data/rlds_dataset"
    validation_output_dir = "validation_output"
    annotations_file = "data/annotations.json"
    
    success = True
    
    # 步骤1: 视频预处理
    if not args.skip_preprocessing:
        if not run_preprocessing(args.data_root, processed_dir, config):
            success = False
    else:
        print("⏭ 跳过视频预处理")
    
    # 步骤2: 动作标注
    if success and not args.skip_annotation:
        if not launch_annotation_tool():
            success = False
    else:
        print("⏭ 跳过动作标注")
    
    # 检查标注文件
    if success:
        if not check_annotations(annotations_file):
            print("⚠ 标注文件有问题，但继续执行...")
    
    # 步骤3: 构建RLDS数据集
    if success:
        if not build_rlds_dataset(args.dataset_name, rlds_output_dir):
            success = False
    
    # 步骤4: 验证数据集
    if success:
        validate_dataset(rlds_output_dir, args.dataset_name, validation_output_dir)
    
    # 总结
    print("\n" + "=" * 50)
    if success:
        print("🎉 数据准备完成!")
        print(f"✓ RLDS数据集: {rlds_output_dir}")
        print(f"✓ 验证结果: {validation_output_dir}")
        print("\n下一步:")
        print("1. 查看验证报告确认数据质量")
        print("2. 将数据集集成到UniVLA项目")
        print("3. 开始模型训练")
        print("\n集成命令:")
        print(f"python data_preparation/integrate_dataset.py \\")
        print(f"    --univla_root /path/to/UniVLA \\")
        print(f"    --dataset_builder_dir data_preparation/human_demo_dataset_builder \\")
        print(f"    --dataset_name {args.dataset_name}")
    else:
        print("❌ 数据准备过程中出现错误")
        print("请检查错误信息并重新运行相应步骤")
    
    return success


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
