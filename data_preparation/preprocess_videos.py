"""
preprocess_videos.py

Video preprocessing script for human demonstration data
Handles 1-2 second video clips and prepares them for RLDS format conversion
"""

import os
import cv2
import json
import numpy as np
import argparse
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from tqdm import tqdm
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed


class VideoPreprocessor:
    """Preprocessor for human demonstration videos."""
    
    def __init__(self, 
                 target_resolution: Tuple[int, int] = (224, 224),
                 target_fps: int = 10,
                 max_frames: int = 32,
                 min_frames: int = 5):
        """
        Initialize video preprocessor.
        
        Args:
            target_resolution: Target image resolution (width, height)
            target_fps: Target frame rate for output
            max_frames: Maximum number of frames per clip
            min_frames: Minimum number of frames per clip
        """
        self.target_resolution = target_resolution
        self.target_fps = target_fps
        self.max_frames = max_frames
        self.min_frames = min_frames
    
    def process_video(self, video_path: str, output_dir: str) -> Optional[Dict]:
        """
        Process a single video file.
        
        Args:
            video_path: Path to input video
            output_dir: Directory to save processed frames
            
        Returns:
            Dictionary with processing metadata or None if failed
        """
        try:
            # Create output directory for this video
            video_name = Path(video_path).stem
            video_output_dir = Path(output_dir) / video_name
            video_output_dir.mkdir(parents=True, exist_ok=True)
            
            # Extract and process frames
            frames_info = self._extract_and_process_frames(video_path, video_output_dir)
            
            if frames_info is None:
                return None
            
            # Get video metadata
            metadata = self._get_video_metadata(video_path)
            
            # Combine information
            result = {
                'video_path': video_path,
                'video_name': video_name,
                'output_dir': str(video_output_dir),
                'category': self._get_video_category(video_path),
                'frames_info': frames_info,
                'metadata': metadata,
                'processed_successfully': True
            }
            
            return result
            
        except Exception as e:
            print(f"Error processing {video_path}: {e}")
            return {
                'video_path': video_path,
                'error': str(e),
                'processed_successfully': False
            }
    
    def _extract_and_process_frames(self, video_path: str, output_dir: Path) -> Optional[Dict]:
        """Extract and process frames from video."""
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            print(f"Error: Could not open video {video_path}")
            return None
        
        # Get video properties
        original_fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = total_frames / original_fps if original_fps > 0 else 0
        
        # Calculate frame sampling strategy
        frame_interval = max(1, int(original_fps / self.target_fps))
        
        frames = []
        frame_indices = []
        frame_count = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Sample frames based on target FPS
            if frame_count % frame_interval == 0:
                # Convert BGR to RGB
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
                # Resize frame
                frame_resized = cv2.resize(frame_rgb, self.target_resolution)
                
                frames.append(frame_resized)
                frame_indices.append(frame_count)
                
                # Save frame as numpy array
                frame_filename = output_dir / f"frame_{len(frames):05d}.npy"
                np.save(frame_filename, frame_resized)
                
                # Limit number of frames
                if len(frames) >= self.max_frames:
                    break
            
            frame_count += 1
        
        cap.release()
        
        # Check if we have enough frames
        if len(frames) < self.min_frames:
            print(f"Warning: Video {video_path} has only {len(frames)} frames (min: {self.min_frames})")
            return None
        
        return {
            'num_frames': len(frames),
            'frame_indices': frame_indices,
            'original_fps': original_fps,
            'target_fps': self.target_fps,
            'duration': duration,
            'frame_interval': frame_interval
        }
    
    def _get_video_metadata(self, video_path: str) -> Dict:
        """Extract metadata from video file."""
        cap = cv2.VideoCapture(video_path)
        
        metadata = {
            'width': int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
            'height': int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
            'fps': cap.get(cv2.CAP_PROP_FPS),
            'frame_count': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
            'duration': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)) / cap.get(cv2.CAP_PROP_FPS) if cap.get(cv2.CAP_PROP_FPS) > 0 else 0,
            'file_size': os.path.getsize(video_path)
        }
        
        cap.release()
        return metadata
    
    def _get_video_category(self, video_path: str) -> str:
        """Determine video category from path."""
        if 'segmented_videos_ok' in video_path:
            return 'successful_operation'
        elif 'segmented_videos_nok_apperence' in video_path:
            return 'failed_appearance'
        elif 'segmented_videos_nok_ele' in video_path:
            return 'failed_electrical'
        else:
            return 'unknown'
    
    def process_dataset(self, 
                       data_root: str, 
                       output_dir: str, 
                       num_workers: int = 4) -> Dict:
        """
        Process entire dataset of videos.
        
        Args:
            data_root: Root directory containing video folders
            output_dir: Output directory for processed data
            num_workers: Number of parallel workers
            
        Returns:
            Dictionary with processing results
        """
        # Find all video files
        video_files = []
        for category in ['segmented_videos_ok', 'segmented_videos_nok_apperence', 'segmented_videos_nok_ele']:
            category_path = Path(data_root) / category
            if category_path.exists():
                video_files.extend(list(category_path.glob('*.mp4')))
        
        print(f"Found {len(video_files)} video files to process")
        
        # Create output directory
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Process videos in parallel
        results = []
        failed_videos = []
        
        if num_workers > 1:
            with ProcessPoolExecutor(max_workers=num_workers) as executor:
                # Submit all tasks
                future_to_video = {
                    executor.submit(self.process_video, str(video_file), output_dir): video_file 
                    for video_file in video_files
                }
                
                # Collect results with progress bar
                for future in tqdm(as_completed(future_to_video), total=len(video_files), desc="Processing videos"):
                    video_file = future_to_video[future]
                    try:
                        result = future.result()
                        if result and result.get('processed_successfully', False):
                            results.append(result)
                        else:
                            failed_videos.append(str(video_file))
                    except Exception as e:
                        print(f"Error processing {video_file}: {e}")
                        failed_videos.append(str(video_file))
        else:
            # Single-threaded processing
            for video_file in tqdm(video_files, desc="Processing videos"):
                result = self.process_video(str(video_file), output_dir)
                if result and result.get('processed_successfully', False):
                    results.append(result)
                else:
                    failed_videos.append(str(video_file))
        
        # Save processing results
        processing_summary = {
            'total_videos': len(video_files),
            'successful_videos': len(results),
            'failed_videos': len(failed_videos),
            'failed_video_list': failed_videos,
            'processing_config': {
                'target_resolution': self.target_resolution,
                'target_fps': self.target_fps,
                'max_frames': self.max_frames,
                'min_frames': self.min_frames
            },
            'results': results
        }
        
        # Save summary to JSON
        summary_path = output_path / 'processing_summary.json'
        with open(summary_path, 'w') as f:
            json.dump(processing_summary, f, indent=2)
        
        print(f"Processing complete: {len(results)}/{len(video_files)} videos processed successfully")
        print(f"Summary saved to: {summary_path}")
        
        return processing_summary


def main():
    """Main function for command line usage."""
    parser = argparse.ArgumentParser(description='Preprocess human demonstration videos')
    parser.add_argument('--data_root', type=str, required=True,
                       help='Root directory containing video folders')
    parser.add_argument('--output_dir', type=str, required=True,
                       help='Output directory for processed data')
    parser.add_argument('--target_fps', type=int, default=10,
                       help='Target frame rate (default: 10)')
    parser.add_argument('--max_frames', type=int, default=32,
                       help='Maximum frames per video (default: 32)')
    parser.add_argument('--min_frames', type=int, default=5,
                       help='Minimum frames per video (default: 5)')
    parser.add_argument('--num_workers', type=int, default=4,
                       help='Number of parallel workers (default: 4)')
    parser.add_argument('--resolution', type=int, nargs=2, default=[224, 224],
                       help='Target resolution [width height] (default: 224 224)')
    
    args = parser.parse_args()
    
    # Create preprocessor
    preprocessor = VideoPreprocessor(
        target_resolution=tuple(args.resolution),
        target_fps=args.target_fps,
        max_frames=args.max_frames,
        min_frames=args.min_frames
    )
    
    # Process dataset
    results = preprocessor.process_dataset(
        data_root=args.data_root,
        output_dir=args.output_dir,
        num_workers=args.num_workers
    )
    
    print(f"Processing completed successfully!")
    print(f"Processed {results['successful_videos']} out of {results['total_videos']} videos")


if __name__ == '__main__':
    main()
