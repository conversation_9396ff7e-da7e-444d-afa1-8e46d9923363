#!/usr/bin/env python3
"""
inference_lam.py

使用预训练的UniVLA Latent Action Model进行推理的脚本
"""

import os
import sys
import torch
import pickle
import argparse
import numpy as np
from pathlib import Path
from tqdm import tqdm
import matplotlib.pyplot as plt
from PIL import Image
import torchvision.transforms as transforms
from huggingface_hub import snapshot_download

# 添加UniVLA路径
sys.path.append('./UniVLA')

from custom_lam_dataset import CustomLAMDataset
from UniVLA.latent_action_model.genie.modules.lam import UncontrolledDINOLatentActionModel, ControllableDINOLatentActionModel

class LAMInference:
    """LAM推理器"""
    
    def __init__(self, args):
        self.args = args
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🔧 使用设备: {self.device}")
        
        # 下载并加载预训练模型
        self.model = self.load_pretrained_model()
        
        # 设置数据
        self.dataset = self.setup_dataset()
        
        # 创建输出目录
        self.output_dir = Path(args.output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def download_pretrained_model(self):
        """下载预训练模型"""
        print("📥 下载预训练LAM模型...")
        
        model_dir = Path("pretrained_lam")
        if not model_dir.exists():
            try:
                snapshot_download(
                    repo_id="qwbu/univla-latent-action-model",
                    local_dir=str(model_dir),
                    local_dir_use_symlinks=False
                )
                print("✅ 模型下载完成")
            except Exception as e:
                print(f"❌ 模型下载失败: {e}")
                print("请手动下载模型或检查网络连接")
                return None
        else:
            print("✅ 使用已下载的模型")
            
        return model_dir
    
    def load_pretrained_model(self):
        """加载预训练模型"""
        # 下载模型
        model_dir = self.download_pretrained_model()
        if model_dir is None:
            raise RuntimeError("无法下载预训练模型")
        
        # 查找检查点文件
        ckpt_files = list(model_dir.glob("*.ckpt")) + list(model_dir.glob("*.pt"))
        if not ckpt_files:
            print("❌ 未找到检查点文件，尝试使用默认模型结构")
            # 使用默认参数创建模型
            model = self.create_default_model()
        else:
            # 加载检查点
            ckpt_path = ckpt_files[0]  # 使用第一个找到的检查点
            print(f"📂 加载检查点: {ckpt_path}")
            
            try:
                checkpoint = torch.load(ckpt_path, map_location=self.device)
                
                # 根据检查点内容确定模型类型
                if 'stage' in checkpoint and checkpoint['stage'] == 'stage-2':
                    model = ControllableDINOLatentActionModel(
                        dino_dim=768,
                        model_dim=768,
                        latent_dim=128,
                        num_latents=16,
                        patch_size=14,
                        enc_blocks=12,
                        dec_blocks=12,
                        num_heads=12,
                        dropout=0.0
                    )
                else:
                    model = UncontrolledDINOLatentActionModel(
                        dino_dim=768,
                        model_dim=768,
                        latent_dim=128,
                        num_latents=16,
                        patch_size=14,
                        enc_blocks=12,
                        dec_blocks=12,
                        num_heads=12,
                        dropout=0.0
                    )
                
                # 加载权重
                if 'model_state_dict' in checkpoint:
                    model.load_state_dict(checkpoint['model_state_dict'])
                elif 'state_dict' in checkpoint:
                    model.load_state_dict(checkpoint['state_dict'])
                else:
                    model.load_state_dict(checkpoint)
                    
                print("✅ 成功加载预训练权重")
                
            except Exception as e:
                print(f"⚠️ 加载检查点失败: {e}")
                print("使用默认模型结构")
                model = self.create_default_model()
        
        model = model.to(self.device)
        model.eval()
        
        print(f"📊 模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
        return model
    
    def create_default_model(self):
        """创建默认模型"""
        return UncontrolledDINOLatentActionModel(
            dino_dim=768,
            model_dim=768,
            latent_dim=128,
            num_latents=16,
            patch_size=14,
            enc_blocks=12,
            dec_blocks=12,
            num_heads=12,
            dropout=0.0
        )
    
    def setup_dataset(self):
        """设置数据集"""
        dataset = CustomLAMDataset(self.args.data_path, augment=False)
        print(f"📊 加载数据集: {len(dataset)} 个样本")
        return dataset
    
    def tensor_to_image(self, tensor):
        """将张量转换为PIL图像"""
        # 反标准化
        mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
        std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
        tensor = tensor * std + mean
        
        # 转换为numpy并调整维度
        tensor = torch.clamp(tensor, 0, 1)
        np_array = tensor.permute(1, 2, 0).cpu().numpy()
        np_array = (np_array * 255).astype(np.uint8)
        
        return Image.fromarray(np_array)
    
    def visualize_results(self, sample_idx, input_frames, reconstructed_frame, latent_codes, task_instruction):
        """可视化推理结果"""
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # 输入的初始帧
        initial_img = self.tensor_to_image(input_frames[0])
        axes[0].imshow(initial_img)
        axes[0].set_title('Initial Frame')
        axes[0].axis('off')
        
        # 输入的目标帧
        target_img = self.tensor_to_image(input_frames[1])
        axes[1].imshow(target_img)
        axes[1].set_title('Target Frame')
        axes[1].axis('off')
        
        # 重建的目标帧
        recon_img = self.tensor_to_image(reconstructed_frame)
        axes[2].imshow(recon_img)
        axes[2].set_title('Reconstructed Frame')
        axes[2].axis('off')
        
        plt.suptitle(f'Sample {sample_idx}: {task_instruction}', fontsize=14)
        plt.tight_layout()
        
        # 保存图像
        save_path = self.output_dir / f'sample_{sample_idx:03d}_visualization.png'
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        return save_path
    
    def save_latent_codes(self, sample_idx, latent_codes, indices, task_instruction, video_path):
        """保存潜在编码"""
        result = {
            'sample_idx': sample_idx,
            'latent_codes': latent_codes.cpu().numpy(),
            'indices': indices.cpu().numpy(),
            'task_instruction': task_instruction,
            'video_path': video_path,
            'shape': latent_codes.shape,
            'codebook_indices': indices.cpu().numpy().tolist()
        }
        
        save_path = self.output_dir / f'sample_{sample_idx:03d}_latent_codes.pkl'
        with open(save_path, 'wb') as f:
            pickle.dump(result, f)
        
        return save_path
    
    def run_inference(self):
        """运行推理"""
        print("🚀 开始LAM推理")
        print("=" * 50)
        
        results = []
        num_samples = min(len(self.dataset), self.args.max_samples)
        
        with torch.no_grad():
            for i in tqdm(range(num_samples), desc="推理进行中"):
                # 获取样本
                sample = self.dataset[i]
                videos = sample['videos'].unsqueeze(0).to(self.device)  # (1, T=2, C, H, W)
                task_instruction = sample['task_instruction']
                video_path = sample.get('video_path', '')
                
                # 准备模型输入
                model_input = {
                    'videos': videos,
                    'task_instruction': [task_instruction]
                }
                
                try:
                    # 前向推理
                    outputs = self.model(model_input)
                    
                    # 提取结果
                    latent_codes = outputs['z_q'][0]  # (T-1, num_codes, latent_dim)
                    indices = outputs['indices'].reshape(-1, self.model.num_codes)  # (T-1, num_codes)
                    reconstructed = outputs['recon'][0, 0]  # 重建的目标帧
                    
                    # 可视化结果
                    if self.args.save_visualizations:
                        viz_path = self.visualize_results(
                            i, videos[0], reconstructed, latent_codes, task_instruction
                        )
                        print(f"💾 可视化保存至: {viz_path}")
                    
                    # 保存潜在编码
                    latent_path = self.save_latent_codes(
                        i, latent_codes, indices, task_instruction, video_path
                    )
                    
                    # 记录结果
                    result = {
                        'sample_idx': i,
                        'task_instruction': task_instruction,
                        'video_path': video_path,
                        'latent_shape': latent_codes.shape,
                        'num_codes': self.model.num_codes,
                        'latent_dim': latent_codes.shape[-1],
                        'codebook_indices': indices[0].cpu().numpy().tolist(),  # 第一帧的编码索引
                        'latent_path': str(latent_path)
                    }
                    
                    if self.args.save_visualizations:
                        result['visualization_path'] = str(viz_path)
                    
                    results.append(result)
                    
                    if (i + 1) % 10 == 0:
                        print(f"✅ 已处理 {i + 1}/{num_samples} 个样本")
                        
                except Exception as e:
                    print(f"❌ 处理样本 {i} 时出错: {e}")
                    continue
        
        # 保存汇总结果
        summary_path = self.output_dir / 'inference_summary.pkl'
        with open(summary_path, 'wb') as f:
            pickle.dump(results, f)
        
        print(f"\n✅ 推理完成!")
        print(f"📊 成功处理: {len(results)}/{num_samples} 个样本")
        print(f"📁 结果保存至: {self.output_dir}")
        print(f"📋 汇总文件: {summary_path}")
        
        # 打印统计信息
        self.print_statistics(results)
        
        return results
    
    def print_statistics(self, results):
        """打印统计信息"""
        if not results:
            return
            
        print("\n📈 推理统计:")
        print("-" * 30)
        
        # 任务类型统计
        task_counts = {}
        for result in results:
            task = result['task_instruction']
            task_counts[task] = task_counts.get(task, 0) + 1
        
        print("任务类型分布:")
        for task, count in task_counts.items():
            print(f"  - {task}: {count} 个样本")
        
        # 潜在编码统计
        if results:
            sample_result = results[0]
            print(f"\n潜在编码信息:")
            print(f"  - 编码形状: {sample_result['latent_shape']}")
            print(f"  - 编码数量: {sample_result['num_codes']}")
            print(f"  - 潜在维度: {sample_result['latent_dim']}")
            
            # 显示第一个样本的编码索引
            print(f"  - 示例编码索引: {sample_result['codebook_indices']}")

def main():
    parser = argparse.ArgumentParser(description='UniVLA LAM Inference')
    parser.add_argument('--data_path', type=str, default='processed_data/train_data.pkl',
                       help='预处理数据文件路径')
    parser.add_argument('--output_dir', type=str, default='inference_results',
                       help='输出目录')
    parser.add_argument('--max_samples', type=int, default=50,
                       help='最大处理样本数')
    parser.add_argument('--save_visualizations', action='store_true',
                       help='保存可视化结果')
    
    args = parser.parse_args()
    
    # 检查数据文件
    if not os.path.exists(args.data_path):
        print(f"❌ 数据文件不存在: {args.data_path}")
        return
    
    # 创建推理器并运行
    inferencer = LAMInference(args)
    results = inferencer.run_inference()
    
    print(f"\n🎉 推理完成! 结果保存在 {args.output_dir} 目录中")

if __name__ == "__main__":
    main()
